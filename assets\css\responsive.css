/* South Safari Developer Partnership Platform - Responsive Styles */
/* Mobile-first responsive design with Olance color scheme */

/* Tablet Styles */
@media (max-width: 1024px) {
    .container {
        padding: 0 15px;
    }
    
    .hero-title {
        font-size: 3rem;
    }
    
    .section-title {
        font-size: 2.25rem;
    }
    
    .categories-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .solution-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .benefits-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Mobile Styles */
@media (max-width: 768px) {
    /* Navigation */
    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        width: 100%;
        background-color: #FFFFFF;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 999;
    }
    
    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }
    
    .nav-links {
        flex-direction: column;
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .nav-toggle.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-5px, 6px);
    }
    
    .nav-toggle.active span:nth-child(2) {
        opacity: 0;
    }
    
    .nav-toggle.active span:nth-child(3) {
        transform: rotate(45deg) translate(-5px, -6px);
    }
    
    /* Hero Section */
    .hero {
        padding: 2rem 0;
        min-height: 60vh;
    }
    
    .hero-title {
        font-size: 2.25rem;
        line-height: 1.3;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .search-form {
        flex-direction: column;
        gap: 12px;
        padding: 12px;
    }
    
    .search-input,
    .search-select {
        padding: 16px;
        font-size: 16px;
    }
    
    .search-btn {
        padding: 16px;
    }
    
    .hero-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .stat-item {
        padding: 1rem;
    }
    
    /* Categories */
    .categories {
        padding: 3rem 0;
    }
    
    .categories-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .category-card {
        padding: 1.5rem;
    }
    
    /* AI Solution */
    .ai-solution {
        padding: 3rem 0;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .section-subtitle {
        font-size: 1rem;
    }
    
    .solution-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .solution-item {
        padding: 1.5rem;
    }
    
    /* Comparison */
    .comparison {
        padding: 3rem 0;
    }
    
    .comparison-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .comparison-column {
        padding: 1.5rem;
    }
    
    /* Signup Sections */
    .signup-sections {
        padding: 3rem 0;
    }
    
    .signup-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .signup-card {
        padding: 2rem;
    }
    
    .signup-title {
        font-size: 1.5rem;
    }
    
    /* Benefits */
    .benefits {
        padding: 3rem 0;
    }
    
    .benefits-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .benefit-item {
        padding: 1.5rem;
    }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }
    
    .hero-title {
        font-size: 1.875rem;
    }
    
    .section-title {
        font-size: 1.75rem;
    }
    
    .hero-subtitle,
    .section-subtitle {
        font-size: 0.875rem;
    }
    
    .btn {
        padding: 14px 20px;
        font-size: 14px;
        width: 100%;
        text-align: center;
    }
    
    .search-form {
        padding: 8px;
    }
    
    .category-card,
    .solution-item,
    .benefit-item,
    .signup-card {
        padding: 1.25rem;
    }
    
    .comparison-column {
        padding: 1.25rem;
    }
    
    .stat-item {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
    
    .stat-icon {
        font-size: 1.5rem;
    }
    
    .solution-icon svg,
    .benefit-icon svg {
        width: 40px;
        height: 40px;
    }
    
    .category-icon svg {
        width: 32px;
        height: 32px;
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    .btn:hover,
    .category-card:hover,
    .solution-item:hover,
    .benefit-item:hover,
    .signup-card:hover {
        transform: none;
    }
    
    .btn:active {
        transform: scale(0.98);
    }
    
    .category-card:active,
    .solution-item:active,
    .benefit-item:active {
        transform: scale(0.99);
    }
}

/* High DPI Display Optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .logo-text {
        font-weight: 600;
    }
    
    .btn {
        font-weight: 600;
    }
    
    .nav-links a {
        font-weight: 500;
    }
}

/* Landscape Mobile Orientation */
@media (max-width: 768px) and (orientation: landscape) {
    .hero {
        min-height: 50vh;
        padding: 1.5rem 0;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-stats {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }
    
    .stat-item {
        padding: 1rem;
    }
}

/* Print Styles */
@media print {
    .nav-toggle,
    .search-form,
    .btn {
        display: none;
    }
    
    .hero,
    .categories,
    .ai-solution,
    .comparison,
    .benefits {
        padding: 1rem 0;
    }
    
    .hero-title,
    .section-title {
        color: #000000 !important;
    }
    
    .highlight {
        color: #000000 !important;
        font-weight: 700;
    }
    
    .category-card,
    .solution-item,
    .benefit-item {
        border: 1px solid #000000;
        break-inside: avoid;
    }
}
