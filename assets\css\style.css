/* South Safari Developer Partnership Platform - Main Styles */
/* Using exact Olance color scheme: Green (#22C55E), White/Light Gray backgrounds, Dark text */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #1F2937;
    background-color: #FFFFFF;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
}

h1 { font-size: 3rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 2rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
    color: #6B7280;
}

.highlight {
    color: #22C55E;
    font-weight: 600;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.btn-primary {
    background-color: #22C55E;
    color: #FFFFFF;
}

.btn-primary:hover {
    background-color: #16A34A;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.btn-secondary {
    background-color: transparent;
    color: #22C55E;
    border: 2px solid #22C55E;
}

.btn-secondary:hover {
    background-color: #22C55E;
    color: #FFFFFF;
}

/* Header */
.header {
    background-color: #FFFFFF;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar {
    padding: 1rem 0;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand .logo {
    text-decoration: none;
    color: #1F2937;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: #22C55E;
}

.logo-subtitle {
    display: block;
    font-size: 0.75rem;
    color: #6B7280;
    font-weight: 400;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
    margin: 0;
}

.nav-links a {
    text-decoration: none;
    color: #1F2937;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-links a:hover {
    color: #22C55E;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background-color: #1F2937;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
    padding: 4rem 0;
    min-height: 70vh;
    display: flex;
    align-items: center;
}

.hero .container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 3rem;
}

.hero-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: #6B7280;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Search Form */
.search-form {
    display: flex;
    background-color: #FFFFFF;
    border-radius: 12px;
    padding: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.search-input {
    flex: 1;
    border: none;
    padding: 12px 16px;
    font-size: 16px;
    outline: none;
    border-radius: 8px;
}

.search-select {
    border: none;
    padding: 12px 16px;
    font-size: 16px;
    outline: none;
    background-color: transparent;
    color: #6B7280;
    min-width: 150px;
}

.search-btn {
    background-color: #22C55E;
    color: #FFFFFF;
    border: none;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.search-btn:hover {
    background-color: #16A34A;
}

.hero-trust {
    color: #6B7280;
    font-size: 14px;
}

/* Hero Stats */
.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    background-color: #FFFFFF;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.stat-icon {
    font-size: 2rem;
}

.stat-number {
    font-size: 1.25rem;
    font-weight: 600;
    color: #22C55E;
}

.stat-label {
    font-size: 0.875rem;
    color: #6B7280;
}

.stat-rating {
    color: #FCD34D;
    font-size: 0.875rem;
}

/* Categories Section */
.categories {
    padding: 4rem 0;
    background-color: #FFFFFF;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.category-card {
    background-color: #FFFFFF;
    border: 1px solid #E5E7EB;
    border-radius: 12px;
    padding: 2rem;
    text-decoration: none;
    transition: all 0.3s ease;
    display: block;
}

.category-card:hover {
    border-color: #22C55E;
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.15);
    transform: translateY(-2px);
}

.category-icon {
    color: #22C55E;
    margin-bottom: 1rem;
}

.category-title {
    color: #1F2937;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.category-count {
    color: #6B7280;
    font-size: 0.875rem;
    margin: 0;
}

/* AI Solution Section */
.ai-solution {
    padding: 4rem 0;
    background-color: #F8FAFC;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    font-size: 2.5rem;
    color: #1F2937;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.125rem;
    color: #6B7280;
    max-width: 600px;
    margin: 0 auto;
}

.solution-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.solution-item {
    background-color: #FFFFFF;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.solution-item:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.solution-icon {
    color: #22C55E;
    margin-bottom: 1.5rem;
}

.solution-title {
    color: #1F2937;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.solution-description {
    color: #6B7280;
    line-height: 1.6;
    margin: 0;
}

/* Comparison Section */
.comparison {
    padding: 4rem 0;
    background-color: #FFFFFF;
}

.comparison-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-top: 2rem;
}

.comparison-column {
    background-color: #F8FAFC;
    border-radius: 12px;
    padding: 2rem;
    border: 2px solid transparent;
}

.comparison-column.traditional {
    border-color: #EF4444;
}

.comparison-column.partnership {
    border-color: #22C55E;
    background-color: #F0FDF4;
}

.comparison-header {
    margin-bottom: 1.5rem;
}

.comparison-header h3 {
    font-size: 1.5rem;
    color: #1F2937;
    margin-bottom: 0.5rem;
}

.comparison-status {
    font-size: 0.875rem;
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    display: inline-block;
}

.comparison-status.danger {
    background-color: #FEE2E2;
    color: #DC2626;
}

.comparison-status.success {
    background-color: #DCFCE7;
    color: #16A34A;
}

.comparison-list {
    list-style: none;
    padding: 0;
}

.comparison-list li {
    padding: 0.75rem 0;
    border-bottom: 1px solid #E5E7EB;
    color: #6B7280;
    position: relative;
    padding-left: 1.5rem;
}

.comparison-list li:last-child {
    border-bottom: none;
}

.comparison-list li:before {
    content: "•";
    position: absolute;
    left: 0;
    color: #22C55E;
    font-weight: bold;
}

.traditional .comparison-list li:before {
    color: #EF4444;
}

/* Signup Sections */
.signup-sections {
    padding: 4rem 0;
    background-color: #F8FAFC;
}

.signup-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
}

.signup-card {
    background-color: #FFFFFF;
    border-radius: 12px;
    padding: 3rem;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.signup-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.signup-card.developer {
    border-top: 4px solid #22C55E;
}

.signup-card.business {
    border-top: 4px solid #3B82F6;
}

.signup-title {
    font-size: 1.75rem;
    color: #1F2937;
    margin-bottom: 1rem;
}

.signup-description {
    color: #6B7280;
    margin-bottom: 2rem;
    line-height: 1.6;
}

/* Benefits Section */
.benefits {
    padding: 4rem 0;
    background-color: #FFFFFF;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.benefit-item {
    background-color: #F8FAFC;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #E5E7EB;
}

.benefit-item:hover {
    background-color: #FFFFFF;
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.15);
    border-color: #22C55E;
    transform: translateY(-2px);
}

.benefit-icon {
    color: #22C55E;
    margin-bottom: 1.5rem;
}

.benefit-title {
    color: #1F2937;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.benefit-description {
    color: #6B7280;
    line-height: 1.6;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.125rem;
    }

    .search-form {
        flex-direction: column;
        gap: 8px;
    }

    .categories-grid {
        grid-template-columns: 1fr;
    }

    .hero-stats {
        grid-template-columns: 1fr;
    }

    .comparison-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .signup-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
    }
}

/* Partnership Page Styles */
.nav-links a.active {
    color: #22C55E;
    font-weight: 600;
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
    padding: 3rem 0;
    text-align: center;
}

.page-title {
    font-size: 2.5rem;
    color: #1F2937;
    margin-bottom: 1rem;
}

.page-subtitle {
    font-size: 1.125rem;
    color: #6B7280;
    max-width: 600px;
    margin: 0 auto 2rem;
}

.page-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 2rem;
}

.page-stats .stat-item {
    text-align: center;
}

.page-stats .stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #22C55E;
}

.page-stats .stat-label {
    font-size: 0.875rem;
    color: #6B7280;
}

/* Search and Filters */
.search-filters {
    background-color: #FFFFFF;
    padding: 2rem 0;
    border-bottom: 1px solid #E5E7EB;
}

.search-filter-form {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.search-group {
    display: flex;
    flex: 1;
    min-width: 300px;
}

.filter-input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #D1D5DB;
    border-radius: 8px 0 0 8px;
    font-size: 14px;
    outline: none;
}

.filter-input:focus {
    border-color: #22C55E;
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.search-group .search-btn {
    padding: 12px 16px;
    background-color: #22C55E;
    color: #FFFFFF;
    border: none;
    border-radius: 0 8px 8px 0;
    cursor: pointer;
}

.filter-select {
    padding: 12px 16px;
    border: 1px solid #D1D5DB;
    border-radius: 8px;
    font-size: 14px;
    background-color: #FFFFFF;
    outline: none;
    min-width: 150px;
}

.filter-select:focus {
    border-color: #22C55E;
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

/* Partnerships Grid */
.partnerships-grid {
    padding: 3rem 0;
    background-color: #F8FAFC;
}

.partnerships-list {
    display: grid;
    gap: 2rem;
}

.partnership-card {
    background-color: #FFFFFF;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid #E5E7EB;
}

.partnership-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.partnership-card.featured {
    border: 2px solid #22C55E;
    position: relative;
}

.partnership-badge {
    position: absolute;
    top: -12px;
    left: 2rem;
    background-color: #22C55E;
    color: #FFFFFF;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

.partnership-header {
    margin-bottom: 1.5rem;
}

.partnership-title {
    font-size: 1.5rem;
    color: #1F2937;
    margin-bottom: 0.5rem;
}

.partnership-meta {
    display: flex;
    gap: 1rem;
}

.partnership-category {
    background-color: #DBEAFE;
    color: #1E40AF;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.partnership-duration {
    background-color: #FEF3C7;
    color: #92400E;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.partnership-description {
    color: #6B7280;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.partnership-requirements,
.partnership-benefits {
    margin-bottom: 1.5rem;
}

.partnership-requirements h4,
.partnership-benefits h4 {
    color: #1F2937;
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.partnership-requirements ul,
.partnership-benefits ul {
    list-style: none;
    padding: 0;
}

.partnership-requirements li,
.partnership-benefits li {
    color: #6B7280;
    padding: 0.25rem 0;
    position: relative;
    padding-left: 1.5rem;
}

.partnership-requirements li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #22C55E;
    font-weight: bold;
}

.partnership-benefits li:before {
    content: "★";
    position: absolute;
    left: 0;
    color: #F59E0B;
    font-weight: bold;
}

.partnership-skills {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.skill-tag {
    background-color: #F3F4F6;
    color: #374151;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.partnership-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1.5rem;
    border-top: 1px solid #E5E7EB;
}

.partnership-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.partnership-market,
.partnership-type {
    font-size: 0.875rem;
    color: #6B7280;
}

/* Partnership CTA */
.partnership-cta {
    background-color: #FFFFFF;
    padding: 4rem 0;
    text-align: center;
}

.cta-title {
    font-size: 2rem;
    color: #1F2937;
    margin-bottom: 1rem;
}

.cta-description {
    font-size: 1.125rem;
    color: #6B7280;
    max-width: 600px;
    margin: 0 auto 2rem;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Application Page Styles */
.application-header {
    background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
    padding: 3rem 0;
    text-align: center;
}

.application-title {
    font-size: 2.5rem;
    color: #1F2937;
    margin-bottom: 1rem;
}

.application-subtitle {
    font-size: 1.125rem;
    color: #6B7280;
    max-width: 600px;
    margin: 0 auto 2rem;
}

.application-benefits {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.benefit-point {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background-color: #FFFFFF;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    font-weight: 500;
    color: #1F2937;
}

.benefit-icon {
    font-size: 1.25rem;
}

/* Application Process */
.application-process {
    padding: 4rem 0;
    background-color: #FFFFFF;
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.process-step {
    text-align: center;
    padding: 2rem;
}

.step-number {
    width: 60px;
    height: 60px;
    background-color: #22C55E;
    color: #FFFFFF;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 auto 1.5rem;
}

.step-title {
    font-size: 1.25rem;
    color: #1F2937;
    margin-bottom: 1rem;
}

.step-description {
    color: #6B7280;
    line-height: 1.6;
}

/* Application Form */
.application-form {
    padding: 4rem 0;
    background-color: #F8FAFC;
}

.form-container {
    max-width: 800px;
    margin: 0 auto;
    background-color: #FFFFFF;
    border-radius: 12px;
    padding: 3rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.form-title {
    font-size: 2rem;
    color: #1F2937;
    margin-bottom: 0.5rem;
    text-align: center;
}

.form-subtitle {
    color: #6B7280;
    text-align: center;
    margin-bottom: 3rem;
}

.form-section {
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #E5E7EB;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.section-heading {
    font-size: 1.5rem;
    color: #1F2937;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #22C55E;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #D1D5DB;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    outline: none;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    border-color: #22C55E;
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.checkbox-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0.75rem;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 8px;
    transition: background-color 0.3s ease;
}

.checkbox-label:hover {
    background-color: #F9FAFB;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
    width: 18px;
    height: 18px;
    accent-color: #22C55E;
}

.checkbox-text {
    font-weight: 400;
    color: #374151;
    line-height: 1.5;
}

.agreement-label {
    background-color: #F0FDF4;
    border: 1px solid #BBF7D0;
    border-radius: 8px;
    padding: 1rem;
}

.agreement-label .checkbox-text {
    font-size: 0.875rem;
    color: #166534;
}

.btn-large {
    padding: 16px 32px;
    font-size: 16px;
    font-weight: 600;
}

.form-submit {
    text-align: center;
    margin-top: 2rem;
}

.submit-note {
    margin-top: 1rem;
    color: #6B7280;
    font-size: 0.875rem;
}

/* About Page Styles */
.about-hero {
    background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
    padding: 4rem 0;
    text-align: center;
}

.about-title {
    font-size: 3rem;
    color: #1F2937;
    margin-bottom: 1.5rem;
}

.about-subtitle {
    font-size: 1.25rem;
    color: #6B7280;
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Mission Section */
.mission {
    padding: 4rem 0;
    background-color: #FFFFFF;
}

.mission-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
}

.mission-description {
    color: #6B7280;
    font-size: 1.125rem;
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.mission-stats {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.stat-card {
    background-color: #F8FAFC;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    border: 1px solid #E5E7EB;
}

.stat-card .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #22C55E;
    display: block;
    margin-bottom: 0.5rem;
}

.stat-card .stat-label {
    color: #6B7280;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Partnership Model */
.partnership-model {
    padding: 4rem 0;
    background-color: #F8FAFC;
}

.model-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.model-card {
    background-color: #FFFFFF;
    padding: 2.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    text-align: center;
}

.model-icon {
    color: #22C55E;
    margin-bottom: 1.5rem;
}

.model-title {
    font-size: 1.5rem;
    color: #1F2937;
    margin-bottom: 1.5rem;
}

.model-benefits {
    list-style: none;
    padding: 0;
    text-align: left;
}

.model-benefits li {
    color: #6B7280;
    padding: 0.5rem 0;
    position: relative;
    padding-left: 1.5rem;
}

.model-benefits li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #22C55E;
    font-weight: bold;
}

/* AI Response Section */
.ai-response {
    padding: 4rem 0;
    background-color: #FFFFFF;
}

.ai-response-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.ai-response-description {
    color: #6B7280;
    font-size: 1.125rem;
    line-height: 1.7;
    margin-bottom: 2rem;
}

.ai-challenges,
.our-solution {
    margin-bottom: 2rem;
}

.ai-challenges h3,
.our-solution h3 {
    color: #1F2937;
    font-size: 1.25rem;
    margin-bottom: 1rem;
}

.ai-challenges ul,
.our-solution ul {
    list-style: none;
    padding: 0;
}

.ai-challenges li,
.our-solution li {
    padding: 0.5rem 0;
    position: relative;
    padding-left: 1.5rem;
    color: #6B7280;
}

.ai-challenges li:before {
    content: "⚠️";
    position: absolute;
    left: 0;
}

.our-solution li:before {
    content: "✅";
    position: absolute;
    left: 0;
}

.comparison-visual {
    background-color: #F8FAFC;
    border-radius: 12px;
    padding: 2rem;
    border: 1px solid #E5E7EB;
}

.traditional-path,
.partnership-path {
    margin-bottom: 2rem;
}

.traditional-path:last-child,
.partnership-path:last-child {
    margin-bottom: 0;
}

.traditional-path h4,
.partnership-path h4 {
    color: #1F2937;
    font-size: 1.125rem;
    margin-bottom: 1rem;
    text-align: center;
}

.path-items {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.path-item {
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    text-align: center;
}

.path-item.negative {
    background-color: #FEE2E2;
    color: #DC2626;
}

.path-item.positive {
    background-color: #DCFCE7;
    color: #16A34A;
}

/* Contact Info Section */
.contact-info {
    padding: 4rem 0;
    background-color: #F8FAFC;
    text-align: center;
}

.contact-description {
    font-size: 1.125rem;
    color: #6B7280;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.contact-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    background-color: #FFFFFF;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.contact-icon {
    font-size: 1.5rem;
}

.contact-text {
    display: flex;
    flex-direction: column;
    text-align: left;
}

.contact-text strong {
    color: #1F2937;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.contact-text span {
    color: #6B7280;
    font-size: 0.875rem;
}

.contact-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Contact Page Styles */
.contact-hero {
    background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
    padding: 3rem 0;
    text-align: center;
}

.contact-title {
    font-size: 2.5rem;
    color: #1F2937;
    margin-bottom: 1rem;
}

.contact-subtitle {
    font-size: 1.125rem;
    color: #6B7280;
    max-width: 600px;
    margin: 0 auto;
}

/* Contact Methods */
.contact-methods {
    padding: 4rem 0;
    background-color: #FFFFFF;
}

.methods-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.method-card {
    background-color: #F8FAFC;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    border: 1px solid #E5E7EB;
    transition: all 0.3s ease;
}

.method-card:hover {
    background-color: #FFFFFF;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.method-icon {
    color: #22C55E;
    margin-bottom: 1.5rem;
}

.method-title {
    font-size: 1.25rem;
    color: #1F2937;
    margin-bottom: 1rem;
}

.method-description {
    color: #6B7280;
    margin-bottom: 1rem;
}

.method-link {
    color: #22C55E;
    font-weight: 600;
    text-decoration: none;
}

.method-link:hover {
    text-decoration: underline;
}

/* Contact Form Section */
.contact-form-section {
    padding: 4rem 0;
    background-color: #F8FAFC;
}

.contact-form-container {
    max-width: 700px;
    margin: 0 auto;
    background-color: #FFFFFF;
    border-radius: 12px;
    padding: 3rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.contact-form .form-grid {
    grid-template-columns: 1fr 1fr;
}

/* FAQ Section */
.faq-section {
    padding: 4rem 0;
    background-color: #FFFFFF;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.faq-item {
    background-color: #F8FAFC;
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid #E5E7EB;
}

.faq-question {
    font-size: 1.125rem;
    color: #1F2937;
    margin-bottom: 1rem;
    font-weight: 600;
}

.faq-answer {
    color: #6B7280;
    line-height: 1.6;
    margin: 0;
}

/* Contact CTA */
.contact-cta {
    padding: 4rem 0;
    background-color: #F8FAFC;
    text-align: center;
}
