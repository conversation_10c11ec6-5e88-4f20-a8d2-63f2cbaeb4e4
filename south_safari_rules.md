# South Safari Platform - Development Rules & Guidelines

## CRITICAL RULES - NEVER VIOLATE THESE

### 1. Partnership Platform Rules (NOT Freelance Marketplace)
- **NEVER use:** "Job", "Freelance", "Contractor", "Gig", "Hire", "Employment"
- **ALWAYS use:** "Partnership", "Developer Partners", "Collaboration", "Alliance"
- **Focus:** Long-term business relationships, not one-off projects
- **Messaging:** Emphasize mutual growth and market expansion opportunities

### 2. User Language & Terminology
- **Developers:** "Developer Partners" or "Partners" (never freelancers)
- **Projects:** "Partnership Opportunities" or "Collaboration Projects"
- **Applications:** "Partnership Applications" or "Partnership Requests"
- **Assignments:** "Partnership Assignments" or "Collaborative Projects"
- **Payments:** "Partnership Compensation" or "Revenue Sharing"

### 3. Regional Focus Requirements
- **Developer Regions:** South Asia (Bangladesh, India, Pakistan, Sri Lanka, Philippines)
- **Target Market:** SADC (Southern African Development Community)
- **Primary Market:** South Africa
- **Language:** Professional English with South African context
- **Currency:** South African Rand (ZAR) where applicable

### 4. Business Model Clarity
- South Safari handles: Business development, client relationships, market knowledge
- Developer Partners handle: Technical development, code delivery, technical support
- **Partnership Focus:** Long-term collaboration over quick projects
- **Value Proposition:** Market access + technical expertise = mutual success

## TECHNICAL DEVELOPMENT RULES

### 5. Local Development Environment
- **Platform:** XAMPP (Apache, MySQL, PHP)
- **URL Structure:** `http://localhost/south-safari/`
- **No External Dependencies:** All assets must be locally hosted
- **Offline Functionality:** Core features must work without internet
- **Database:** MySQL with complete setup scripts and sample data

### 6. Security Standards (Non-Negotiable)
- **Input Validation:** All user inputs must be validated and sanitized
- **SQL Injection Prevention:** Use prepared statements exclusively
- **XSS Protection:** Escape all output, use CSP headers
- **Password Security:** Minimum 8 characters, hashed with password_hash()
- **File Uploads:** Validate file types, sizes, and scan for malicious content
- **Session Management:** Secure session handling with proper timeout

### 7. Code Quality Standards
- **PHP Standards:** Follow PSR-12 coding standards
- **Comments:** Every function and complex logic must be commented
- **Error Handling:** Proper try-catch blocks and error logging
- **File Organization:** Clear folder structure with separation of concerns
- **Database Queries:** Efficient queries with proper indexing

## USER EXPERIENCE RULES

### 8. Design & Navigation
- **Professional Appearance:** Clean, modern design reflecting serious business
- **Mobile-First:** Responsive design for all screen sizes
- **Load Time:** Pages must load in under 3 seconds
- **Navigation:** Intuitive menu structure with clear user paths
- **Accessibility:** WCAG 2.1 AA compliance for all users

### 9. Content Guidelines
- **Tone:** Professional, confident, partnership-focused
- **Language Level:** Clear and accessible to non-technical users
- **Call-to-Actions:** Clear, compelling, partnership-oriented
- **Error Messages:** Helpful, specific, and solution-oriented
- **Success Messages:** Positive reinforcement of partnership benefits

### 10. User Role Boundaries
- **Public Users:** Browse only, no access to dashboards
- **Developer Partners:** Own dashboard and projects only
- **Admin:** Full access but must maintain professional boundaries
- **Data Privacy:** Users can only see their own data (except admin)

## FUNCTIONALITY RULES

### 11. Authentication & Authorization
- **Registration:** Email verification required for all new partners
- **Login:** Secure session management with proper logout
- **Password Recovery:** Secure token-based reset system
- **Role-Based Access:** Strict enforcement of user permissions
- **Account Lockout:** After 5 failed login attempts

### 12. Database Management
- **Data Integrity:** Foreign key constraints and referential integrity
- **Backup Strategy:** Regular automated backups with easy restore
- **Performance:** Efficient queries with proper indexing
- **Data Validation:** Server-side validation for all database operations
- **Audit Trail:** Log all important user actions and changes

### 13. Communication System
- **Professional Messaging:** All communications must maintain business tone
- **Email Integration:** Multiple email addresses as specified in PRD
- **Notification System:** Real-time updates for important events
- **Message History:** Complete conversation threads for reference
- **Privacy:** Secure messaging with no unauthorized access

## CONTENT & MESSAGING RULES

### 14. Project Descriptions
- **Focus:** Market opportunities and business potential
- **Technical Requirements:** Clear but not overwhelming
- **Partnership Benefits:** Emphasize long-term value
- **Success Stories:** Real examples of successful partnerships
- **Timeline:** Realistic expectations with milestone clarity

### 15. Developer Onboarding
- **Welcome Process:** Warm, professional introduction to platform
- **Portfolio Guidelines:** Clear requirements for showcasing work
- **Skill Assessment:** Fair evaluation of technical capabilities
- **Partnership Agreement:** Clear terms and expectations
- **Support System:** Easy access to help and guidance

## CONTACT & SUPPORT RULES

### 16. Contact Information Standards
- **Primary Phone:** +27 67 975 7128 (always format consistently)
- **Location:** Johannesburg, South Africa (never just "South Africa")
- **Email System:** Use appropriate email for specific purposes:
  - <EMAIL> (Primary partnerships)
  - <EMAIL> (Administrative)
  - <EMAIL> (Technical support)
  - <EMAIL> (Project communications)

### 17. Response Standards
- **Response Time:** Clear expectations for communication response
- **Professional Tone:** All communications reflect business partnership
- **Problem Resolution:** Clear escalation path for issues
- **Support Hours:** Defined availability for different time zones
- **Documentation:** Comprehensive help resources and FAQs

## TESTING & QUALITY ASSURANCE RULES

### 18. Testing Requirements
- **Functionality Testing:** All features must work perfectly on XAMPP
- **Security Testing:** Penetration testing for all input points
- **Performance Testing:** Load testing with realistic user numbers
- **Browser Testing:** Cross-browser compatibility verification
- **Mobile Testing:** Full functionality on mobile devices

### 19. Launch Preparation
- **Sample Data:** Realistic sample projects and user profiles
- **Admin Setup:** Complete admin account with proper permissions
- **Documentation:** Complete setup and user guides
- **Backup System:** Tested backup and restore procedures
- **Monitoring:** Error logging and performance monitoring

## ERROR HANDLING & MAINTENANCE RULES

### 20. Error Management
- **User-Friendly Errors:** Clear, helpful error messages
- **Logging System:** Comprehensive error logging for debugging
- **Recovery Procedures:** Clear paths for error recovery
- **Escalation Process:** Defined steps for critical issues
- **Monitoring:** Proactive error detection and alerting

### 21. Future Development
- **Scalability:** Code structure must support future growth
- **Feature Expansion:** Modular design for easy feature addition
- **Integration Ready:** API structure for third-party integrations
- **Performance Optimization:** Efficient code for scaling
- **Documentation:** Maintain comprehensive development documentation

## COMPLIANCE & LEGAL RULES

### 22. Data Protection
- **Privacy Policy:** Clear data handling procedures
- **GDPR Compliance:** European user data protection
- **South African Compliance:** Local data protection requirements
- **IP Protection:** Secure handling of intellectual property
- **Confidentiality:** Strict confidentiality for all projects

### 23. Terms of Service
- **Partnership Terms:** Clear partnership agreements
- **Dispute Resolution:** Fair and clear dispute procedures
- **Termination Process:** Professional relationship ending procedures
- **Intellectual Property:** Clear IP ownership and usage rights
- **Liability:** Appropriate liability limitations and protections

## DEPLOYMENT & OPERATIONS RULES

### 24. Production Deployment
- **Environment Parity:** Production must match development environment
- **Security Hardening:** Additional security measures for production
- **Performance Optimization:** Caching and optimization for live use
- **Monitoring:** Comprehensive monitoring and alerting
- **Backup Strategy:** Regular, tested backups with quick restore

### 25. Ongoing Operations
- **Regular Updates:** Scheduled maintenance and updates
- **Security Patches:** Prompt application of security updates
- **Performance Monitoring:** Continuous performance optimization
- **User Feedback:** Regular collection and implementation of feedback
- **Business Growth:** Platform evolution to support business growth

---

## DEVELOPMENT CHECKLIST

Before considering any feature complete, verify:
- [ ] Follows all partnership language rules
- [ ] Implements proper security measures
- [ ] Works perfectly on local XAMPP
- [ ] Includes proper error handling
- [ ] Has responsive design
- [ ] Includes comprehensive testing
- [ ] Follows professional design standards
- [ ] Maintains user role boundaries
- [ ] Includes proper documentation
- [ ] Supports future scalability

## EMERGENCY CONTACT RULES

If any development decisions conflict with these rules:
1. **STOP development immediately**
2. **Document the conflict clearly**
3. **Seek clarification before proceeding**
4. **Never compromise on partnership messaging**
5. **Always prioritize security and user experience**

---

**Remember:** This is a professional business platform connecting South Asian developers with African market opportunities. Every decision should reflect this serious business purpose and partnership focus.